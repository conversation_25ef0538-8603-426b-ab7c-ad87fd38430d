.login {
    width: 400px;
    max-width: 90%;
    background: #fff;
    border: 1px solid #dddfe2;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 2rem;
    align-items: center;
    text-align: center;
    margin: 50px auto;
}

.login h1 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.login > input {
    border-radius: 8px;
    border: 2px solid #dddfe2;
    outline: none;
    color: #1d2129;
    margin: 0.75rem 0;
    padding: 0.75rem 1rem;
    width: 92%;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.login > input:focus {
    border-color: #1877f2;
}

.login > .button {
    background: #1877f2;
    border: 1px solid #1877f2;
    color: #fff;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 0.75rem 1rem;
    margin: 1rem 0 0.5rem 0;
    border-radius: 8px;
    outline: none;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
}

.login > .button:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.login > div {
    margin: 0.5rem 0;
    color: #666;
    font-size: 0.9rem;
}

@media (max-width: 480px) {
    .login {
        width: 95%;
        padding: 1.5rem;
        margin: 20px auto;
    }

    .login h1 {
        font-size: 1.6rem;
    }
}