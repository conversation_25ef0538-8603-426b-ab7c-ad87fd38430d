import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import axios from 'axios'
import './attemptQuiz.css'

const AttemptQuiz = ({ user }) => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [quiz, setQuiz] = useState(null)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    fetchQuiz()
  }, [id])

  const fetchQuiz = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`http://localhost:3000/api/quiz/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      setQuiz(response.data.quiz)
      setAnswers(new Array(response.data.quiz.questions.length).fill(-1))
    } catch (error) {
      console.error('Error fetching quiz:', error)
      alert('Failed to load quiz')
      navigate('/dashboard')
    } finally {
      setLoading(false)
    }
  }

  const handleAnswerSelect = (answerIndex) => {
    const newAnswers = [...answers]
    newAnswers[currentQuestion] = answerIndex
    setAnswers(newAnswers)
  }

  const goToQuestion = (questionIndex) => {
    setCurrentQuestion(questionIndex)
  }

  const nextQuestion = () => {
    if (currentQuestion < quiz.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
    }
  }

  const prevQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1)
    }
  }

  const submitQuiz = async () => {
    if (user.role !== 'Student') {
      alert('Only students can submit quiz answers')
      return
    }

    if (answers.includes(-1)) {
      if (!window.confirm('You have unanswered questions. Are you sure you want to submit?')) {
        return
      }
    }

    // Keep -1 for unanswered questions
    const processedAnswers = answers

    console.log('Original answers:', answers)
    console.log('Processed answers:', processedAnswers)
    console.log('Quiz ID:', id)
    console.log('User:', user)

    setSubmitting(true)
    try {
      const token = localStorage.getItem('token')
      console.log('Token:', token ? 'Present' : 'Missing')

      const response = await axios.post(
        `http://localhost:3000/api/quiz/${id}/submit`,
        { answers: processedAnswers },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      )

      console.log('Response:', response.data)
      alert(`Quiz submitted! Your score: ${response.data.data.totalScore}/${response.data.data.maxScore}`)
      navigate('/dashboard')
    } catch (error) {
      console.error('Error submitting quiz:', error)
      console.error('Error response:', error.response)
      console.error('Error status:', error.response?.status)
      console.error('Error data:', error.response?.data)
      alert(error.response?.data?.message || 'Failed to submit quiz')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading quiz...</div>
      </div>
    )
  }

  if (!quiz) {
    return (
      <div className="error-container">
        <div className="error-message">
          <h2>Quiz not found</h2>
          <button className="btn btn-primary" onClick={() => navigate('/dashboard')}>
            Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  if (!quiz.isOpen && user.role === 'Student') {
    return (
      <div className="error-container">
        <div className="error-message">
          <h2>Quiz is closed</h2>
          <p>This quiz is not currently open for submissions.</p>
          <button className="btn btn-primary" onClick={() => navigate('/dashboard')}>
            Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  const currentQ = quiz.questions[currentQuestion]
  const answeredCount = answers.filter(ans => ans !== -1).length

  return (
    <div className="attempt-quiz-container">
      <div className="quiz-header">
        <div className="quiz-info">
          <h1>{quiz.title}</h1>
          <p>Teacher: {quiz.teacher.name}</p>
        </div>
        <div className="quiz-progress">
          <span>Question {currentQuestion + 1} of {quiz.questions.length}</span>
          <span>Answered: {answeredCount}/{quiz.questions.length}</span>
        </div>
      </div>

      <div className="question-navigation">
        <div className="question-numbers">
          {quiz.questions.map((_, index) => (
            <div
              key={index}
              className={`question-number ${
                index === currentQuestion ? 'current' :
                answers[index] !== -1 ? 'answered' : 'unanswered'
              }`}
              onClick={() => goToQuestion(index)}
            >
              {index + 1}
            </div>
          ))}
        </div>
      </div>

      <div className="question-container">
        <div className="question-header">
          <h2>Question {currentQuestion + 1}</h2>
          <div className="question-marks">{currentQ.marks} marks</div>
        </div>

        <div className="question-text">
          <p>{currentQ.title}</p>
        </div>

        <div className="answer-options">
          {currentQ.answer.map((option, index) => (
            <div
              key={index}
              className={`answer-option ${answers[currentQuestion] === index ? 'selected' : ''}`}
              onClick={() => handleAnswerSelect(index)}
            >
              <input
                type="radio"
                name={`question-${currentQuestion}`}
                checked={answers[currentQuestion] === index}
                onChange={() => handleAnswerSelect(index)}
              />
              <div className="answer-text">{option}</div>
            </div>
          ))}
        </div>
      </div>

      <div className="quiz-navigation">
        <button
          className="btn btn-secondary"
          onClick={prevQuestion}
          disabled={currentQuestion === 0}
        >
          Previous
        </button>

        <div className="nav-center">
          <button
            className="btn btn-primary"
            onClick={submitQuiz}
            disabled={submitting || user.role !== 'Student'}
          >
            {submitting ? 'Submitting...' :
             user.role === 'Student' ? 'Submit Quiz' : 'Preview Only (Teacher)'}
          </button>
        </div>

        <button
          className="btn btn-secondary"
          onClick={nextQuestion}
          disabled={currentQuestion === quiz.questions.length - 1}
        >
          Next
        </button>
      </div>
    </div>
  )
}

export default AttemptQuiz
