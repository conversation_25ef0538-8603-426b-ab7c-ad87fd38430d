import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'
import './CreateQuiz.css'

const CreateQuiz = ({ user }) => {
  const navigate = useNavigate()

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      navigate('/login')
      return
    }

    if (user && user.role && user.role !== 'Teacher') {
      alert('Only teachers can create quizzes')
      navigate('/dashboard')
    }
  }, [user, navigate])
  const [quiz, setQuiz] = useState({
    title: '',
    isOpen: false,
    questions: []
  })
  const [currentQuestion, setCurrentQuestion] = useState({
    title: '',
    answer: ['', '', '', ''],
    correctIndex: 0,
    marks: 1
  })

  const handleQuizChange = (e) => {
    const { name, value, type, checked } = e.target
    setQuiz(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleQuestionChange = (e) => {
    const { name, value } = e.target
    setCurrentQuestion(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleAnswerChange = (index, value) => {
    setCurrentQuestion(prev => ({
      ...prev,
      answer: prev.answer.map((ans, i) => i === index ? value : ans)
    }))
  }

  const addQuestion = () => {
    if (currentQuestion.title && currentQuestion.answer.every(ans => ans.trim())) {
      setQuiz(prev => ({
        ...prev,
        questions: [...prev.questions, { ...currentQuestion }]
      }))
      setCurrentQuestion({
        title: '',
        answer: ['', '', '', ''],
        correctIndex: 0,
        marks: 1
      })
    } else {
      alert('Please fill all question fields and answers')
    }
  }

  const removeQuestion = (index) => {
    setQuiz(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }))
  }

  const createQuiz = async () => {
    if (!quiz.title || quiz.questions.length === 0) {
      alert('Please provide quiz title and at least one question')
      return
    }

    try {
      const token = localStorage.getItem('token')
      await axios.post(
        'http://localhost:3000/api/quiz/create',
        quiz,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      )
      alert('Quiz created successfully!')
      navigate('/dashboard')
    } catch (error) {
      console.error('Error creating quiz:', error)
      alert(error.response?.data?.message || 'Failed to create quiz')
    }
  }

  return (
    <div className="create-quiz-container">
      <div className="header">
        <h1>Create New Quiz</h1>
        <button onClick={() => navigate('/dashboard')}>
          Back to Dashboard
        </button>
      </div>

      <div className="form">
        <div className="basic-info">
          <label>Quiz Title</label>
          <input
            type="text"
            name="title"
            value={quiz.title}
            onChange={handleQuizChange}
            placeholder="Enter quiz title"
          />

          <label>
            <input
              type="checkbox"
              name="isOpen"
              checked={quiz.isOpen}
              onChange={handleQuizChange}
            />
            Quiz is open for students
          </label>
        </div>

        <div className="questions">
          <h2>Questions ({quiz.questions.length})</h2>

          {quiz.questions.map((question, index) => (
            <div key={index} className="question">
              <div className="question-header">
                <h3>Question {index + 1}</h3>
                <span>Marks: {question.marks}</span>
                <button onClick={() => removeQuestion(index)}>
                  Remove
                </button>
              </div>
              <p><strong>{question.title}</strong></p>
              <div>
                {question.answer.map((ans, i) => (
                  <div key={i}>
                    {i + 1}. {ans} {i === question.correctIndex && '✓'}
                  </div>
                ))}
              </div>
            </div>
          ))}

          <div className="new-question">
            <h3>Add New Question</h3>
            <input
              type="number"
              name="marks"
              value={currentQuestion.marks}
              onChange={handleQuestionChange}
              min="1"
              placeholder="Marks"
            />

            <label>Question</label>
            <textarea
              name="title"
              value={currentQuestion.title}
              onChange={handleQuestionChange}
              placeholder="Enter your question"
              rows="3"
            />

            <label>Answer Options (Select the correct answer)</label>
            {currentQuestion.answer.map((answer, index) => (
              <div key={index} className="answer-option">
                <input
                  type="text"
                  value={answer}
                  onChange={(e) => handleAnswerChange(index, e.target.value)}
                  placeholder={`Option ${index + 1}`}
                />
                <label>
                  <input
                    type="radio"
                    name="correctAnswer"
                    checked={currentQuestion.correctIndex === index}
                    onChange={() => setCurrentQuestion(prev => ({ ...prev, correctIndex: index }))}
                  />
                  Correct
                </label>
              </div>
            ))}

            <button onClick={addQuestion}>Add Question</button>
          </div>
        </div>

        <button onClick={createQuiz}>Create Quiz</button>
      </div>
    </div>
  )
}

export default CreateQuiz
