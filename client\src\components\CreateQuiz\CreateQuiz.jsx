import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'

const CreateQuiz = () => {
  const navigate = useNavigate()
  const [quiz, setQuiz] = useState({
    title: '',
    isOpen: false,
    questions: []
  })
  const [currentQuestion, setCurrentQuestion] = useState({
    title: '',
    answer: ['', '', '', ''],
    correctIndex: 0,
    marks: 1
  })

  const handleQuizChange = (e) => {
    const { name, value, type, checked } = e.target
    setQuiz(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleQuestionChange = (e) => {
    const { name, value } = e.target
    setCurrentQuestion(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleAnswerChange = (index, value) => {
    setCurrentQuestion(prev => ({
      ...prev,
      answer: prev.answer.map((ans, i) => i === index ? value : ans)
    }))
  }

  const addQuestion = () => {
    if (currentQuestion.title && currentQuestion.answer.every(ans => ans.trim())) {
      setQuiz(prev => ({
        ...prev,
        questions: [...prev.questions, { ...currentQuestion }]
      }))
      setCurrentQuestion({
        title: '',
        answer: ['', '', '', ''],
        correctIndex: 0,
        marks: 1
      })
    } else {
      alert('Please fill all question fields and answers')
    }
  }

  const removeQuestion = (index) => {
    setQuiz(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }))
  }

  const createQuiz = async () => {
    if (!quiz.title || quiz.questions.length === 0) {
      alert('Please provide quiz title and at least one question')
      return
    }

    try {
      const token = localStorage.getItem('token')
      await axios.post(
        'http://localhost:3000/api/quiz/create',
        quiz,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      )
      alert('Quiz created successfully!')
      navigate('/dashboard')
    } catch (error) {
      console.error('Error creating quiz:', error)
      alert(error.response?.data?.message || 'Failed to create quiz')
    }
  }

  return (
    <div className="create-quiz-container">
      <div className="create-quiz-header">
        <h1>Create New Quiz</h1>
        <div
          className="btn btn-secondary"
          onClick={() => navigate('/dashboard')}
          style={{ cursor: 'pointer' }}
        >
          Back to Dashboard
        </div>
      </div>

      <div className="create-quiz-form">
        <div className="quiz-basic-info">
          <div className="form-group">
            <label>Quiz Title</label>
            <input
              type="text"
              name="title"
              value={quiz.title}
              onChange={handleQuizChange}
              className="form-input"
              placeholder="Enter quiz title"
            />
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                name="isOpen"
                checked={quiz.isOpen}
                onChange={handleQuizChange}
              />
              Quiz is open for students
            </label>
          </div>
        </div>

        <div className="questions-section">
          <div className="questions-header">
            <h2>Questions ({quiz.questions.length})</h2>
          </div>

          {quiz.questions.map((question, index) => (
            <div key={index} className="question-form">
              <div className="question-header">
                <h3>Question {index + 1}</h3>
                <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                  <span>Marks: {question.marks}</span>
                  <button
                    className="btn btn-danger btn-small"
                    onClick={() => removeQuestion(index)}
                  >
                    Remove
                  </button>
                </div>
              </div>
              <p><strong>{question.title}</strong></p>
              <div>
                {question.answer.map((ans, i) => (
                  <div key={i} style={{ margin: '5px 0' }}>
                    <span style={{
                      color: i === question.correctIndex ? '#28a745' : '#666',
                      fontWeight: i === question.correctIndex ? 'bold' : 'normal'
                    }}>
                      {i + 1}. {ans} {i === question.correctIndex && '✓'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}

          <div className="question-form">
            <div className="question-header">
              <h3>Add New Question</h3>
              <input
                type="number"
                name="marks"
                value={currentQuestion.marks}
                onChange={handleQuestionChange}
                className="marks-input"
                min="1"
                placeholder="Marks"
              />
            </div>

            <div className="form-group">
              <label>Question</label>
              <textarea
                name="title"
                value={currentQuestion.title}
                onChange={handleQuestionChange}
                className="form-textarea"
                placeholder="Enter your question"
                rows="3"
              />
            </div>

            <div className="answers-section">
              <div className="answers-header">
                <label>Answer Options</label>
                <span className="radio-text">Select the correct answer</span>
              </div>

              {currentQuestion.answer.map((answer, index) => (
                <div key={index} className="answer-option">
                  <div className="answer-input-group">
                    <input
                      type="text"
                      value={answer}
                      onChange={(e) => handleAnswerChange(index, e.target.value)}
                      className="answer-input form-input"
                      placeholder={`Option ${index + 1}`}
                    />
                    <label className="radio-label">
                      <input
                        type="radio"
                        name="correctAnswer"
                        checked={currentQuestion.correctIndex === index}
                        onChange={() => setCurrentQuestion(prev => ({ ...prev, correctIndex: index }))}
                      />
                      Correct
                    </label>
                  </div>
                </div>
              ))}
            </div>

            <div style={{ textAlign: 'center', marginTop: '20px' }}>
              <button className="btn btn-outline" onClick={addQuestion}>
                Add Question
              </button>
            </div>
          </div>
        </div>

        <div className="form-actions">
          <button className="btn btn-primary btn-large" onClick={createQuiz}>
            Create Quiz
          </button>
        </div>
      </div>
    </div>
  )
}

export default CreateQuiz
