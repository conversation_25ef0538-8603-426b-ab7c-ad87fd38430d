import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import axios from 'axios'
import './result.css'

const Result = ({ user }) => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [quiz, setQuiz] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('summary')

  useEffect(() => {
    fetchQuiz()
  }, [id])

  const fetchQuiz = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`http://localhost:3000/api/quiz/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      setQuiz(response.data.quiz)
    } catch (error) {
      console.error('Error fetching quiz:', error)
      alert('Failed to load quiz results')
      navigate('/dashboard')
    } finally {
      setLoading(false)
    }
  }

  const getGrade = (percentage) => {
    if (percentage >= 90) return { grade: 'A', class: 'grade-a' }
    if (percentage >= 80) return { grade: 'B', class: 'grade-b' }
    if (percentage >= 70) return { grade: 'C', class: 'grade-c' }
    if (percentage >= 60) return { grade: 'D', class: 'grade-d' }
    return { grade: 'F', class: 'grade-f' }
  }

  const calculateStats = () => {
    if (!quiz || !quiz.responses) return null

    const totalMarks = quiz.questions.reduce((sum, q) => sum + q.marks, 0)
    const responses = quiz.responses

    if (responses.length === 0) {
      return { totalMarks, responses: [], averageScore: 0, highestScore: 0, lowestScore: 0 }
    }

    const scores = responses.map(r => r.totalScore)
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const highestScore = Math.max(...scores)
    const lowestScore = Math.min(...scores)

    return {
      totalMarks,
      responses,
      averageScore: Math.round(averageScore * 100) / 100,
      highestScore,
      lowestScore
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading results...</div>
      </div>
    )
  }

  if (!quiz) {
    return (
      <div className="error-container">
        <div className="error-message">
          <h2>Quiz not found</h2>
          <button className="btn btn-primary" onClick={() => navigate('/dashboard')}>
            Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  const stats = calculateStats()
  const totalMarks = quiz.questions.reduce((sum, q) => sum + q.marks, 0)

  return (
    <div className="result-container">
      <div className="result-header">
        <h1>Quiz Results</h1>
        <h2>{quiz.title}</h2>
        <p>Teacher: {quiz.teacher.name}</p>
      </div>

      <div className="result-tabs">
        <button
          className={`tab ${activeTab === 'summary' ? 'active' : ''}`}
          onClick={() => setActiveTab('summary')}
        >
          Summary
        </button>
        {user.role === 'Teacher' && (
          <button
            className={`tab ${activeTab === 'all-results' ? 'active' : ''}`}
            onClick={() => setActiveTab('all-results')}
          >
            All Results
          </button>
        )}
      </div>

      {activeTab === 'summary' && (
        <div className="result-summary">
          <div className="quiz-stats">
            <h3>Quiz Statistics</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span>Total Questions:</span>
                <span>{quiz.questions.length}</span>
              </div>
              <div className="stat-item">
                <span>Total Marks:</span>
                <span>{totalMarks}</span>
              </div>
              <div className="stat-item">
                <span>Status:</span>
                <span className={quiz.isOpen ? 'status-open' : 'status-closed'}>
                  {quiz.isOpen ? 'Open' : 'Closed'}
                </span>
              </div>
              <div className="stat-item">
                <span>Total Responses:</span>
                <span>{stats?.responses.length || 0}</span>
              </div>
              {stats && stats.responses.length > 0 && (
                <>
                  <div className="stat-item">
                    <span>Average Score:</span>
                    <span>{stats.averageScore}/{totalMarks}</span>
                  </div>
                  <div className="stat-item">
                    <span>Highest Score:</span>
                    <span>{stats.highestScore}/{totalMarks}</span>
                  </div>
                  <div className="stat-item">
                    <span>Lowest Score:</span>
                    <span>{stats.lowestScore}/{totalMarks}</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'all-results' && user.role === 'Teacher' && (
        <div className="all-results">
          <h3>All Student Results</h3>
          {stats && stats.responses.length > 0 ? (
            <div className="results-table">
              <table>
                <thead>
                  <tr>
                    <th>Student</th>
                    <th>Score</th>
                    <th>Percentage</th>
                    <th>Grade</th>
                    <th>Submitted</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.responses.map((response, index) => {
                    const percentage = Math.round((response.totalScore / totalMarks) * 100)
                    const gradeInfo = getGrade(percentage)
                    return (
                      <tr key={index}>
                        <td>{response.student?.name || 'Unknown'}</td>
                        <td>{response.totalScore}/{totalMarks}</td>
                        <td>{percentage}%</td>
                        <td>
                          <span className={`grade ${gradeInfo.class}`}>
                            {gradeInfo.grade}
                          </span>
                        </td>
                        <td>{new Date(response.submittedAt).toLocaleString()}</td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="no-result">
              <h3>No submissions yet</h3>
              <p>Students haven't submitted any responses for this quiz.</p>
            </div>
          )}
        </div>
      )}

      <div className="result-actions">
        <button
          className="btn btn-secondary"
          onClick={() => navigate('/dashboard')}
        >
          Back to Dashboard
        </button>
        {user.role === 'Teacher' && (
          <button
            className="btn btn-outline"
            onClick={() => navigate(`/quiz/${id}`)}
          >
            Preview Quiz
          </button>
        )}
      </div>
    </div>
  )
}

export default Result
