import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import axios from 'axios'
import './result.css'

const Result = ({ user }) => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [quiz, setQuiz] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchQuiz = async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await axios.get(`http://localhost:3000/api/quiz/${id}`, {
          headers: { Authorization: `Bearer ${token}` }
        })
        setQuiz(response.data.quiz)
      } catch (error) {
        console.error('Error fetching quiz:', error)
        navigate('/dashboard')
      } finally {
        setLoading(false)
      }
    }
    fetchQuiz()
  }, [id, navigate])

  const getTotalMarks = () => {
    return quiz.questions.reduce((sum, q) => sum + q.marks, 0)
  }

  if (loading) return <div>Loading...</div>
  if (!quiz) return <div>Quiz not found</div>

  const totalMarks = getTotalMarks()
  const responses = quiz.responses || []

  return (
    <div>
      <h1>Quiz Results: {quiz.title}</h1>
      <p>Teacher: {quiz.teacher.name}</p>

      <div>
        <h3>Quiz Info</h3>
        <p>Questions: {quiz.questions.length}</p>
        <p>Total Marks: {totalMarks}</p>
        <p>Status: {quiz.isOpen ? 'Open' : 'Closed'}</p>
        <p>Total Responses: {responses.length}</p>
      </div>

      {user.role === 'Teacher' && responses.length > 0 && (
        <div>
          <h3>Student Results</h3>
          <table>
            <thead>
              <tr>
                <th>Student</th>
                <th>Score</th>
                <th>Percentage</th>
                <th>Submitted</th>
              </tr>
            </thead>
            <tbody>
              {responses.map((response, index) => {
                const percentage = Math.round((response.totalScore / totalMarks) * 100)
                return (
                  <tr key={index}>
                    <td>{response.student?.name || 'Unknown'}</td>
                    <td>{response.totalScore}/{totalMarks}</td>
                    <td>{percentage}%</td>
                    <td>{new Date(response.submittedAt).toLocaleString()}</td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      )}

      {user.role === 'Teacher' && responses.length === 0 && (
        <p>No student submissions yet.</p>
      )}

      <button onClick={() => navigate('/dashboard')}>
        Back to Dashboard
      </button>
    </div>
  )
}

export default Result
