.home-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.welcome-section h1 {
  color: #333;
  margin-bottom: 15px;
  font-size: 2.5rem;
}

.user-role-badge {
  background-color: #1877f2;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 16px;
  display: inline-block;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.3s ease;
  border: 1px solid #dddfe2;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.stat-card h3 {
  color: #666;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
}

.stat-number {
  font-size: 48px;
  font-weight: bold;
  color: #1877f2;
  margin-bottom: 10px;
}

.stat-card p {
  color: #888;
  font-size: 14px;
  margin: 0;
}

.quick-actions {
  margin-bottom: 40px;
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quick-actions h2 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.8rem;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-block;
  border: none;
  font-size: 16px;
  cursor: pointer;
}

.action-btn.primary {
  background-color: #1877f2;
  color: white;
}

.action-btn.primary:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.action-btn.secondary {
  background-color: #6c757d;
  color: white;
}

.action-btn.secondary:hover {
  background-color: #545b62;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.student-section {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #dddfe2;
}

.student-section h2 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.6rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  padding: 20px;
  font-size: 18px;
  color: #666;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
  .home-container {
    padding: 15px;
  }

  .welcome-section h1 {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    text-align: center;
  }
}