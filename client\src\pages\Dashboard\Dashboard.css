.quizzes-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.quizzes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.quizzes-header h1 {
  color: #333;
  margin: 0;
  font-size: 2rem;
}

.quizzes-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px;
  flex-wrap: wrap;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 10px 20px;
  border: 2px solid #1877f2;
  background-color: transparent;
  color: #1877f2;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
  background-color: #1877f2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.quizzes-grid {
  display: grid;
  gap: 20px;
}

.quiz-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  padding: 25px;
  transition: all 0.3s ease;
  border: 1px solid #dddfe2;
}

.quiz-card:hover {
  box-shadow: 0 8px 16px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.quiz-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.quiz-card-header h3 {
  color: #333;
  margin: 0;
  font-size: 1.4rem;
}

.status-badge {
  padding: 6px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.open {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.closed {
  background-color: #f8d7da;
  color: #721c24;
}

.quiz-card-body {
  margin-bottom: 20px;
}

.quiz-info p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.quiz-info strong {
  color: #333;
}

.quiz-card-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 100px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background-color: #1877f2;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-outline {
  background-color: transparent;
  color: #1877f2;
  border: 2px solid #1877f2;
}

.btn-outline:hover {
  background-color: #1877f2;
  color: white;
}

.btn-disabled {
  background-color: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.btn-disabled:hover {
  background-color: #e9ecef;
  transform: none;
  box-shadow: none;
}

.no-quizzes {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.no-quizzes h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5rem;
}

.no-quizzes p {
  font-size: 16px;
  line-height: 1.6;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  padding: 20px;
  font-size: 18px;
  color: #666;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
  .quizzes-container {
    padding: 15px;
  }

  .quizzes-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .quizzes-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-buttons {
    justify-content: center;
  }

  .quiz-card {
    padding: 20px;
  }

  .quiz-card-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .quiz-card-actions {
    justify-content: stretch;
  }

  .quiz-card-actions .btn {
    flex: 1;
  }
}