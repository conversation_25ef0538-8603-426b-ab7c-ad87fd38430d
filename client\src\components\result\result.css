.result-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.result-header {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.result-header h1 {
  color: #333;
  margin-bottom: 15px;
  font-size: 2rem;
}

.result-header h2 {
  color: #1877f2;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.result-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.quiz-summary {
  background: white;
  padding: 25px;
  border-radius: 8px;
  border: 1px solid #ddd;
  margin-bottom: 20px;
}

.quiz-summary h3 {
  margin-bottom: 20px;
  color: #333;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.summary-item span:first-child {
  color: #666;
  font-weight: 500;
}

.summary-item span:last-child {
  color: #333;
  font-weight: 600;
}

.status-open {
  color: #28a745;
}

.status-closed {
  color: #dc3545;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #1877f2;
  margin-bottom: 5px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.student-results {
  background: white;
  padding: 25px;
  border-radius: 8px;
  border: 1px solid #ddd;
  margin-bottom: 20px;
}

.student-results h3 {
  margin-bottom: 20px;
  color: #333;
}

.table-container {
  overflow-x: auto;
}

.table-container table {
  width: 100%;
  border-collapse: collapse;
}

.table-container th,
.table-container td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table-container th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

.table-container tr:hover {
  background-color: #f8f9fa;
}

.no-results {
  background: white;
  padding: 40px;
  border-radius: 8px;
  border: 1px solid #ddd;
  text-align: center;
  margin-bottom: 20px;
}

.no-results h3 {
  color: #333;
  margin-bottom: 10px;
}

.no-results p {
  color: #666;
  margin: 0;
}

.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.back-btn,
.preview-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.back-btn {
  background-color: #6c757d;
  color: white;
}

.preview-btn {
  background-color: #1877f2;
  color: white;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-text,
.error-text {
  padding: 20px;
  font-size: 18px;
  color: #666;
  background: white;
  border-radius: 8px;
  border: 1px solid #ddd;
}

@media (max-width: 768px) {
  .result-container {
    padding: 15px;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .result-actions {
    flex-direction: column;
  }

  .back-btn,
  .preview-btn {
    width: 100%;
  }
}