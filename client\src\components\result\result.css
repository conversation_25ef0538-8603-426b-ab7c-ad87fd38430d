.result-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.result-header {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #dddfe2;
}

.result-header h1 {
  color: #333;
  margin-bottom: 15px;
  font-size: 2.5rem;
}

.result-header h2 {
  color: #1877f2;
  margin-bottom: 10px;
  font-size: 1.8rem;
}

.result-header p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.result-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

.tab {
  padding: 15px 30px;
  background: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  flex: 1;
}

.tab:hover,
.tab.active {
  color: #1877f2;
  border-bottom-color: #1877f2;
  background-color: #f8f9fa;
}

.result-summary {
  display: grid;
  gap: 30px;
}

.quiz-stats {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #dddfe2;
}

.quiz-stats h3 {
  margin-bottom: 25px;
  color: #333;
  font-size: 1.6rem;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item span:first-child {
  color: #666;
  font-weight: 500;
}

.stat-item span:last-child {
  color: #333;
  font-weight: 600;
}

.status-open {
  color: #28a745;
  font-weight: 600;
}

.status-closed {
  color: #dc3545;
  font-weight: 600;
}

.all-results {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #dddfe2;
}

.all-results h3 {
  margin-bottom: 25px;
  color: #333;
  font-size: 1.6rem;
  text-align: center;
}

.results-table {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #dddfe2;
}

.results-table table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.results-table th,
.results-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.results-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.results-table tr:hover {
  background-color: #f8f9fa;
}

.results-table tr:last-child td {
  border-bottom: none;
}

.grade {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.grade-a {
  background-color: #d4edda;
  color: #155724;
}

.grade-b {
  background-color: #cce5ff;
  color: #004085;
}

.grade-c {
  background-color: #fff3cd;
  color: #856404;
}

.grade-d {
  background-color: #f8d7da;
  color: #721c24;
}

.grade-f {
  background-color: #f5c6cb;
  color: #721c24;
}

.no-result {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-result h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.5rem;
}

.no-result p {
  font-size: 16px;
  line-height: 1.6;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 140px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background-color: #1877f2;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-outline {
  background-color: transparent;
  color: #1877f2;
  border: 2px solid #1877f2;
}

.btn-outline:hover {
  background-color: #1877f2;
  color: white;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  padding: 30px;
  font-size: 18px;
  color: #666;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 30px;
  border-radius: 10px;
  border: 1px solid #f5c6cb;
  text-align: center;
  max-width: 500px;
}

.error-message h2 {
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.error-message p {
  margin-bottom: 20px;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .result-container {
    padding: 15px;
  }

  .result-header {
    padding: 20px;
  }

  .result-header h1 {
    font-size: 2rem;
  }

  .result-header h2 {
    font-size: 1.4rem;
  }

  .result-tabs {
    flex-direction: column;
  }

  .tab {
    padding: 12px 20px;
    font-size: 14px;
  }

  .quiz-stats,
  .all-results {
    padding: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .results-table th,
  .results-table td {
    padding: 10px;
    font-size: 14px;
  }

  .result-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}