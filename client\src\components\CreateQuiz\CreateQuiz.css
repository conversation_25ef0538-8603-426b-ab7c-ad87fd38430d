.create-quiz-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.form {
  background: white;
  padding: 20px;
  border: 1px solid #ddd;
}

.quiz-basic-info {
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #dddfe2;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #1877f2;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.2);
}

.questions-section {
  margin-bottom: 30px;
}

.questions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.questions-header h2 {
  color: #333;
  margin: 0;
  font-size: 1.6rem;
}

.question-form {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 20px;
  border: 2px solid #e9ecef;
  transition: border-color 0.3s ease;
}

.question-form:hover {
  border-color: #1877f2;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.question-header h3 {
  color: #333;
  margin: 0;
  font-size: 1.2rem;
}

.marks-input {
  width: 80px;
  padding: 8px;
  border: 2px solid #dddfe2;
  border-radius: 6px;
  font-size: 14px;
}

.answers-section {
  margin-top: 20px;
}

.answers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.answers-header label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.radio-text {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.answer-option {
  margin-bottom: 15px;
}

.answer-input-group {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.answer-input {
  flex: 1;
  min-width: 200px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.radio-label input[type="radio"] {
  margin-right: 8px;
  transform: scale(1.2);
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background-color: #1877f2;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-outline {
  background-color: transparent;
  color: #1877f2;
  border: 2px solid #1877f2;
}

.btn-outline:hover {
  background-color: #1877f2;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-small {
  padding: 8px 16px;
  font-size: 12px;
  min-width: auto;
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.form-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 2px solid #eee;
}

@media (max-width: 768px) {
  .create-quiz-container {
    padding: 15px;
  }

  .create-quiz-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .create-quiz-form {
    padding: 20px;
  }

  .question-form {
    padding: 20px;
  }

  .question-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .answer-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .radio-label {
    justify-content: center;
  }
}