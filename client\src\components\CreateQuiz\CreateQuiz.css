.create-quiz-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 1.8rem;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.section {
  background: white;
  padding: 25px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.section h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.4rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.input,
.textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #1877f2;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 10px;
}

.checkbox-label span {
  color: #333;
}

.quiz-basic-info {
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 2px solid #eee;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #dddfe2;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #1877f2;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.2);
}

.questions-section {
  margin-bottom: 30px;
}

.questions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.questions-header h2 {
  color: #333;
  margin: 0;
  font-size: 1.6rem;
}

.question-form {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 20px;
  border: 2px solid #e9ecef;
  transition: border-color 0.3s ease;
}

.question-form:hover {
  border-color: #1877f2;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.question-header h3 {
  color: #333;
  margin: 0;
  font-size: 1.2rem;
}

.marks-input {
  width: 80px;
  padding: 8px;
  border: 2px solid #dddfe2;
  border-radius: 6px;
  font-size: 14px;
}

.answers-section {
  margin-top: 20px;
}

.answers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.answers-header label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.radio-text {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.answer-option {
  margin-bottom: 15px;
}

.answer-input-group {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.answer-input {
  flex: 1;
  min-width: 200px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.radio-label input[type="radio"] {
  margin-right: 8px;
  transform: scale(1.2);
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-end;
}

.marks-group {
  min-width: 120px;
}

.marks-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.add-question-form {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-top: 20px;
}

.add-question-form h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
}

.answer-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.answer-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.option-label {
  font-weight: 600;
  min-width: 25px;
  color: #333;
}

.option-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  white-space: nowrap;
}

.add-question-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin-top: 15px;
}

.form-actions {
  text-align: center;
  margin-top: 20px;
}

.create-quiz-btn {
  background: #1877f2;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  min-width: 200px;
}

.create-quiz-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #ddd;
  margin-bottom: 20px;
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}

.question-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.question-info h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.marks-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.question-content {
  padding: 20px;
}

.question-text {
  margin: 0 0 15px 0;
  font-weight: 500;
  color: #333;
  font-size: 16px;
}

.answers-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.answer-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.answer-item.correct {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.answer-label {
  font-weight: 600;
  min-width: 20px;
}

.answer-text {
  flex: 1;
}

.correct-mark {
  color: #28a745;
  font-weight: bold;
  font-size: 16px;
}

@media (max-width: 768px) {
  .create-quiz-container {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .answer-option {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .question-header {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .question-info {
    justify-content: space-between;
  }
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
  background-color: #1877f2;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-outline {
  background-color: transparent;
  color: #1877f2;
  border: 2px solid #1877f2;
}

.btn-outline:hover {
  background-color: #1877f2;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.btn-small {
  padding: 8px 16px;
  font-size: 12px;
  min-width: auto;
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

.form-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 2px solid #eee;
}

@media (max-width: 768px) {
  .create-quiz-container {
    padding: 15px;
  }

  .create-quiz-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .create-quiz-form {
    padding: 20px;
  }

  .question-form {
    padding: 20px;
  }

  .question-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .answer-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .radio-label {
    justify-content: center;
  }
}