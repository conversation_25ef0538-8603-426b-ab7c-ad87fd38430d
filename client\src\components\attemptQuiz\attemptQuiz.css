.attempt-quiz-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.quiz-header {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #dddfe2;
}

.quiz-info h1 {
  color: #333;
  margin-bottom: 8px;
  font-size: 2rem;
}

.quiz-info p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.quiz-progress {
  text-align: right;
  color: #666;
  font-size: 14px;
}

.quiz-progress span {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.question-navigation {
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  border: 1px solid #dddfe2;
}

.question-numbers {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.question-number {
  width: 45px;
  height: 45px;
  border: 2px solid #dddfe2;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.question-number:hover {
  border-color: #1877f2;
  transform: scale(1.1);
}

.question-number.current {
  background-color: #1877f2;
  color: white;
  border-color: #1877f2;
}

.question-number.answered {
  background-color: #28a745;
  color: white;
  border-color: #28a745;
}

.question-number.unanswered {
  border-color: #dc3545;
  color: #dc3545;
}

.question-container {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  border: 1px solid #dddfe2;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid #eee;
  flex-wrap: wrap;
  gap: 10px;
}

.question-header h2 {
  color: #333;
  margin: 0;
  font-size: 1.4rem;
}

.question-marks {
  background-color: #1877f2;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.question-text {
  margin-bottom: 25px;
}

.question-text p {
  font-size: 18px;
  line-height: 1.6;
  color: #333;
  margin: 0;
}

.answer-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.answer-option {
  display: flex;
  align-items: center;
  padding: 18px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.answer-option:hover {
  border-color: #1877f2;
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.answer-option.selected {
  border-color: #1877f2;
  background-color: #e3f2fd;
  box-shadow: 0 4px 8px rgba(24, 119, 242, 0.2);
}

.answer-option input[type="radio"] {
  margin-right: 15px;
  transform: scale(1.3);
  accent-color: #1877f2;
}

.answer-text {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

.quiz-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #dddfe2;
}

.nav-center {
  flex: 1;
  text-align: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
  background-color: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
  transform: none;
}

.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

.btn-primary {
  background-color: #1877f2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  padding: 30px;
  font-size: 18px;
  color: #666;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 30px;
  border-radius: 10px;
  border: 1px solid #f5c6cb;
  text-align: center;
  max-width: 500px;
}

.error-message h2 {
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.error-message p {
  margin-bottom: 20px;
  line-height: 1.6;
}

.quiz-completed-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.score-display {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
  border: 1px solid #dddfe2;
}

.score-display h1 {
  color: #28a745;
  margin-bottom: 30px;
  font-size: 2.5rem;
}

.score-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 10px;
  margin: 20px 0;
  border: 2px solid #28a745;
}

.score-card h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.score-number {
  font-size: 3rem;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 10px;
}

.score-percentage {
  font-size: 1.5rem;
  color: #666;
  margin-bottom: 15px;
}

.score-card p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.quiz-info {
  margin: 20px 0;
  padding: 20px;
  background: #e3f2fd;
  border-radius: 8px;
}

.quiz-info h3 {
  color: #1877f2;
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.quiz-info p {
  color: #666;
  margin: 0;
}

.score-display button {
  background-color: #1877f2;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .attempt-quiz-container {
    padding: 15px;
  }

  .quiz-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .quiz-progress {
    text-align: center;
  }

  .question-numbers {
    justify-content: center;
  }

  .question-number {
    width: 40px;
    height: 40px;
    font-size: 12px;
  }

  .question-container {
    padding: 20px;
  }

  .question-header {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .question-text p {
    font-size: 16px;
  }

  .answer-option {
    padding: 15px;
  }

  .answer-text {
    font-size: 14px;
  }

  .quiz-navigation {
    flex-direction: column;
    gap: 15px;
  }

  .nav-center {
    order: -1;
  }
}