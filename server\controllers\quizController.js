const Quiz = require("../models/Quiz");
const User = require("../models/User");
const mongoose = require("mongoose");

exports.createQuiz = async (req, res) => {
  try {
    const { title, questions, isOpen } = req.body;
    const teacherId = req.user.id;

    if (!title || !questions || questions.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Title and at least one question are required",
      });
    }

    const newQuiz = await Quiz.create({
      title,
      isOpen: isOpen ?? false,
      questions,
      teacher: teacherId,
    });

    await User.findByIdAndUpdate(teacherId, {
      $push: { createdQuizzes: newQuiz._id },
    });

    return res.status(201).json({
      success: true,
      message: "Quiz created successfully",
      quiz: newQuiz,
    });
  } catch (error) {
    console.error("Create quiz error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error while creating quiz",
    });
  }
};

exports.getAllQuiz = async (req, res) => {
  try {
    const quizzes = await Quiz.find()
      .populate("teacher", "name email")
      .select("-responses");

    return res.status(200).json({
      success: true,
      quizzes,
    });
  } catch (error) {
    console.error("Get all quizzes error:", error);
    return res.status(500).json({
      success: false,
      message: "Error while fetching quizzes",
    });
  }
};

exports.getQuiz = async (req, res) => {
  try {
    const { id } = req.params;

    const quiz = await Quiz.findById(id)
      .populate("teacher", "name email")
      .populate("responses.student", "name email");

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: "Quiz not found",
      });
    }

    return res.status(200).json({
      success: true,
      quiz,
    });
  } catch (error) {
    console.error("Get quiz error:", error);
    return res.status(500).json({
      success: false,
      message: "Error while fetching quiz",
    });
  }
};

exports.submitQuiz = async (req, res) => {
  try {
    const quizId = req.params.id;
    const studentId = req.user.id;
    const { answers = [] } = req.body;
    const quiz = await Quiz.findById(quizId);

    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: "Quiz not found",
      });
    }
    if (!quiz.isOpen) {
      return res.status(403).json({
        success: false,
        message: "Quiz is closed",
      });
    }

    let score = 0;
    let totalMarks = 0;

    for (let i = 0; i < quiz.questions.length; i++) {
      const question = quiz.questions[i];
      const studentAnswer = answers[i];

      // Ensure question.marks is a valid number, default to 1 if not
      const questionMarks = typeof question.marks === 'number' && !isNaN(question.marks) ? question.marks : 1;

      console.log(`Question ${i + 1}:`, {
        questionMarks,
        studentAnswer,
        correctIndex: question.correctIndex,
        isCorrect: studentAnswer === question.correctIndex && studentAnswer !== -1
      });

      totalMarks += questionMarks;

      if (studentAnswer !== -1 && studentAnswer === question.correctIndex) {
        score += questionMarks;
      }
    }


    // Validate that score and totalMarks are valid numbers
    if (isNaN(score) || isNaN(totalMarks)) {
      console.error("Invalid score calculation:", { score, totalMarks });
      return res.status(400).json({
        success: false,
        message: "Error calculating quiz score",
      });
    }

    quiz.responses.push({
      student: studentId,
      answers,
      totalScore: score,
    });

    await quiz.save();
    console.log("Quiz saved successfully");

    return res.status(200).json({
      success: true,
      message: "Quiz submitted successfully",
      data: { totalScore: score, maxScore: totalMarks },
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Server error while submitting quiz",
      error: error.message,
    });
  }
};

exports.toggleQuizStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const teacherId = req.user.id;

    const quiz = await Quiz.findById(id);
    if (!quiz) {
      return res.status(404).json({
        success: false,
        message: "Quiz not found",
      });
    }

    if (quiz.teacher.toString() !== teacherId) {
      return res.status(403).json({
        success: false,
        message: "You can only modify your own quizzes",
      });
    }

    quiz.isOpen = !quiz.isOpen;
    await quiz.save();

    return res.status(200).json({
      success: true,
      message: `Quiz ${quiz.isOpen ? "opened" : "closed"} successfully`,
      quiz: {
        id: quiz._id,
        title: quiz.title,
        isOpen: quiz.isOpen,
      },
    });
  } catch (error) {
    console.error("Toggle quiz status error:", error);
    return res.status(500).json({
      success: false,
      message: "Error while updating quiz status",
    });
  }
};
